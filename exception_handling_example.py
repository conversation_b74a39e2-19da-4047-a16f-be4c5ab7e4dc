"""
异常处理问题示例 - 错误处理和边界情况问题
这个文件包含了常见的异常处理问题，用于教学和调试练习
"""

import json
import os
import requests
from typing import List, Dict, Optional


def poor_exception_handling_1(filename):
    """
    问题1: 捕获所有异常但不处理
    这样会隐藏真正的问题
    """
    try:
        with open(filename, 'r') as f:
            return f.read()
    except:  # 问题：捕获所有异常
        pass  # 问题：不处理异常，静默失败


def poor_exception_handling_2(data):
    """
    问题2: 异常信息不明确
    """
    try:
        return int(data)
    except:
        raise Exception("Something went wrong")  # 问题：异常信息不具体


def poor_exception_handling_3(numbers):
    """
    问题3: 没有处理边界情况
    """
    return numbers[0] / numbers[1]  # 问题：没有检查列表长度和除零


def poor_exception_handling_4(url):
    """
    问题4: 资源没有正确释放
    """
    try:
        response = requests.get(url, timeout=5)
        return response.json()
    except requests.RequestException:
        return None
    # 问题：没有确保连接被关闭


def poor_exception_handling_5(data_list):
    """
    问题5: 在循环中处理异常不当
    """
    results = []
    for item in data_list:
        try:
            result = process_item(item)
            results.append(result)
        except:
            continue  # 问题：静默跳过错误，可能丢失重要信息
    return results


def process_item(item):
    """辅助函数"""
    if item < 0:
        raise ValueError("Negative value not allowed")
    return item * 2


# 改进的异常处理示例

def better_file_handling(filename):
    """
    改进版本1: 具体的异常处理
    """
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        raise FileNotFoundError(f"文件 '{filename}' 不存在")
    except PermissionError:
        raise PermissionError(f"没有权限读取文件 '{filename}'")
    except UnicodeDecodeError as e:
        raise UnicodeDecodeError(
            e.encoding, e.object, e.start, e.end,
            f"文件 '{filename}' 编码错误: {e.reason}"
        )
    except IOError as e:
        raise IOError(f"读取文件 '{filename}' 时发生IO错误: {e}")


def better_data_conversion(data, data_type=int):
    """
    改进版本2: 明确的异常信息和类型检查
    """
    if data is None:
        raise ValueError("输入数据不能为None")
    
    if isinstance(data, str) and not data.strip():
        raise ValueError("输入字符串不能为空")
    
    try:
        if data_type == int:
            return int(data)
        elif data_type == float:
            return float(data)
        else:
            raise ValueError(f"不支持的数据类型: {data_type}")
    except ValueError as e:
        if "invalid literal" in str(e):
            raise ValueError(f"无法将 '{data}' 转换为 {data_type.__name__}")
        raise


def better_division(numbers):
    """
    改进版本3: 完整的边界检查
    """
    if not isinstance(numbers, (list, tuple)):
        raise TypeError("输入必须是列表或元组")
    
    if len(numbers) < 2:
        raise ValueError("至少需要两个数字进行除法运算")
    
    dividend, divisor = numbers[0], numbers[1]
    
    if not isinstance(dividend, (int, float)) or not isinstance(divisor, (int, float)):
        raise TypeError("除法运算的操作数必须是数字")
    
    if divisor == 0:
        raise ZeroDivisionError("除数不能为零")
    
    return dividend / divisor


def better_api_request(url, timeout=10, retries=3):
    """
    改进版本4: 完整的资源管理和重试机制
    """
    if not url or not isinstance(url, str):
        raise ValueError("URL必须是非空字符串")
    
    last_exception = None
    
    for attempt in range(retries):
        try:
            with requests.Session() as session:
                response = session.get(url, timeout=timeout)
                response.raise_for_status()  # 检查HTTP状态码
                return response.json()
                
        except requests.exceptions.Timeout as e:
            last_exception = e
            print(f"请求超时 (尝试 {attempt + 1}/{retries}): {e}")
            
        except requests.exceptions.ConnectionError as e:
            last_exception = e
            print(f"连接错误 (尝试 {attempt + 1}/{retries}): {e}")
            
        except requests.exceptions.HTTPError as e:
            last_exception = e
            print(f"HTTP错误 (尝试 {attempt + 1}/{retries}): {e}")
            
        except json.JSONDecodeError as e:
            raise ValueError(f"响应不是有效的JSON格式: {e}")
            
        except requests.exceptions.RequestException as e:
            last_exception = e
            print(f"请求异常 (尝试 {attempt + 1}/{retries}): {e}")
    
    # 所有重试都失败了
    raise ConnectionError(f"经过 {retries} 次重试后仍无法连接到 {url}: {last_exception}")


def better_batch_processing(data_list, continue_on_error=False):
    """
    改进版本5: 详细的错误记录和可配置的错误处理
    """
    if not isinstance(data_list, (list, tuple)):
        raise TypeError("输入必须是列表或元组")
    
    results = []
    errors = []
    
    for index, item in enumerate(data_list):
        try:
            result = process_item(item)
            results.append(result)
            
        except ValueError as e:
            error_info = {
                'index': index,
                'item': item,
                'error_type': 'ValueError',
                'error_message': str(e)
            }
            errors.append(error_info)
            
            if not continue_on_error:
                raise ValueError(f"处理第 {index} 个项目时出错: {e}") from e
                
        except Exception as e:
            error_info = {
                'index': index,
                'item': item,
                'error_type': type(e).__name__,
                'error_message': str(e)
            }
            errors.append(error_info)
            
            if not continue_on_error:
                raise RuntimeError(f"处理第 {index} 个项目时发生未预期的错误: {e}") from e
    
    return {
        'results': results,
        'errors': errors,
        'success_count': len(results),
        'error_count': len(errors)
    }


class CustomError(Exception):
    """自定义异常类"""
    def __init__(self, message, error_code=None, details=None):
        super().__init__(message)
        self.error_code = error_code
        self.details = details or {}


def advanced_error_handling_example(config_file):
    """
    高级异常处理示例：自定义异常和上下文管理
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(config_file):
            raise CustomError(
                f"配置文件不存在: {config_file}",
                error_code="FILE_NOT_FOUND",
                details={'file_path': config_file}
            )
        
        # 读取和解析配置文件
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        # 验证配置
        required_keys = ['database_url', 'api_key', 'timeout']
        missing_keys = [key for key in required_keys if key not in config_data]
        
        if missing_keys:
            raise CustomError(
                "配置文件缺少必需的键",
                error_code="MISSING_CONFIG_KEYS",
                details={'missing_keys': missing_keys}
            )
        
        return config_data
        
    except json.JSONDecodeError as e:
        raise CustomError(
            "配置文件JSON格式错误",
            error_code="INVALID_JSON",
            details={
                'line': e.lineno,
                'column': e.colno,
                'message': e.msg
            }
        ) from e
        
    except CustomError:
        # 重新抛出自定义异常
        raise
        
    except Exception as e:
        # 包装未预期的异常
        raise CustomError(
            "读取配置文件时发生未知错误",
            error_code="UNKNOWN_ERROR",
            details={'original_error': str(e)}
        ) from e


# 测试代码
if __name__ == "__main__":
    print("异常处理示例测试:")
    print("=" * 50)
    
    # 测试1: 文件处理
    print("1. 文件处理异常:")
    try:
        result = better_file_handling("nonexistent_file.txt")
    except FileNotFoundError as e:
        print(f"   捕获异常: {e}")
    
    # 测试2: 数据转换
    print("\n2. 数据转换异常:")
    try:
        result = better_data_conversion("abc", int)
    except ValueError as e:
        print(f"   捕获异常: {e}")
    
    # 测试3: 除法运算
    print("\n3. 除法运算异常:")
    try:
        result = better_division([10, 0])
    except ZeroDivisionError as e:
        print(f"   捕获异常: {e}")
    
    # 测试4: 批处理
    print("\n4. 批处理结果:")
    test_data = [1, 2, -3, 4, "invalid", 5]
    result = better_batch_processing(test_data, continue_on_error=True)
    print(f"   成功处理: {result['success_count']} 个")
    print(f"   错误数量: {result['error_count']} 个")
    print(f"   结果: {result['results']}")
    if result['errors']:
        print("   错误详情:")
        for error in result['errors']:
            print(f"     索引 {error['index']}: {error['error_message']}")
    
    # 测试5: 自定义异常
    print("\n5. 自定义异常:")
    try:
        config = advanced_error_handling_example("missing_config.json")
    except CustomError as e:
        print(f"   错误代码: {e.error_code}")
        print(f"   错误信息: {e}")
        print(f"   错误详情: {e.details}")
    
    print("\n" + "=" * 50)
    print("异常处理最佳实践:")
    print("1. 捕获具体的异常类型，而不是使用裸露的except")
    print("2. 提供有意义的错误信息")
    print("3. 正确处理资源释放")
    print("4. 记录错误信息用于调试")
    print("5. 使用自定义异常类提供更多上下文")
    print("6. 在适当的层级处理异常")
    print("7. 考虑是否需要重试机制")
