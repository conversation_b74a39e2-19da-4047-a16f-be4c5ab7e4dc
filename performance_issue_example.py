"""
性能问题示例 - 算法效率和资源使用问题
这个文件包含了常见的性能问题，用于教学和优化练习
"""

import time
import sys
from functools import lru_cache


def inefficient_fibonacci(n):
    """
    问题1: 指数时间复杂度
    递归计算斐波那契数，时间复杂度O(2^n)
    """
    if n <= 1:
        return n
    return inefficient_fibonacci(n-1) + inefficient_fibonacci(n-2)


@lru_cache(maxsize=None)
def efficient_fibonacci(n):
    """
    优化版本：使用缓存，时间复杂度O(n)
    """
    if n <= 1:
        return n
    return efficient_fibonacci(n-1) + efficient_fibonacci(n-2)


def inefficient_prime_check(n):
    """
    问题2: 低效的质数检查
    时间复杂度O(n)，对大数很慢
    """
    if n < 2:
        return False
    for i in range(2, n):  # 检查所有数字
        if n % i == 0:
            return False
    return True


def efficient_prime_check(n):
    """
    优化版本：只检查到sqrt(n)，时间复杂度O(sqrt(n))
    """
    if n < 2:
        return False
    if n == 2:
        return True
    if n % 2 == 0:
        return False
    
    for i in range(3, int(n**0.5) + 1, 2):
        if n % i == 0:
            return False
    return True


def inefficient_string_concat(words):
    """
    问题3: 低效的字符串拼接
    每次拼接都创建新字符串，时间复杂度O(n^2)
    """
    result = ""
    for word in words:
        result += word + " "  # 每次都创建新字符串
    return result.strip()


def efficient_string_concat(words):
    """
    优化版本：使用join，时间复杂度O(n)
    """
    return " ".join(words)


def inefficient_list_search(data, target):
    """
    问题4: 重复的线性搜索
    在循环中多次搜索同一个列表
    """
    results = []
    for i in range(len(data)):
        if target in data:  # 每次都重新搜索整个列表
            results.append(i)
    return results


def efficient_list_search(data, target):
    """
    优化版本：预先检查一次
    """
    if target not in data:
        return []
    return [i for i, item in enumerate(data) if item == target]


def memory_inefficient_processing(data):
    """
    问题5: 内存使用低效
    创建不必要的中间列表
    """
    # 创建多个中间列表
    doubled = [x * 2 for x in data]
    filtered = [x for x in doubled if x > 10]
    squared = [x ** 2 for x in filtered]
    return sum(squared)


def memory_efficient_processing(data):
    """
    优化版本：使用生成器表达式
    """
    return sum(x ** 2 for x in (x * 2 for x in data) if x * 2 > 10)


def inefficient_nested_loops(matrix):
    """
    问题6: 不必要的嵌套循环
    计算矩阵所有元素的和
    """
    total = 0
    for i in range(len(matrix)):
        for j in range(len(matrix[i])):
            for k in range(1):  # 不必要的第三层循环
                total += matrix[i][j]
    return total


def efficient_matrix_sum(matrix):
    """
    优化版本：使用内置函数
    """
    return sum(sum(row) for row in matrix)


def inefficient_duplicate_removal(lst):
    """
    问题7: 低效的去重算法
    时间复杂度O(n^2)
    """
    result = []
    for item in lst:
        if item not in result:  # 每次都搜索整个result列表
            result.append(item)
    return result


def efficient_duplicate_removal(lst):
    """
    优化版本：使用集合，时间复杂度O(n)
    """
    return list(dict.fromkeys(lst))  # 保持顺序的去重


def inefficient_sorting(data):
    """
    问题8: 重复排序
    多次对同一数据排序
    """
    results = []
    for i in range(5):
        sorted_data = sorted(data)  # 每次都重新排序
        results.append(sorted_data[0] if sorted_data else None)
    return results


def efficient_sorting(data):
    """
    优化版本：排序一次
    """
    if not data:
        return [None] * 5
    sorted_data = sorted(data)
    return [sorted_data[0]] * 5


class InefficientDataStructure:
    """
    问题9: 使用不当的数据结构
    频繁的插入和查找操作使用列表
    """
    def __init__(self):
        self.data = []  # 使用列表存储
    
    def add(self, item):
        if item not in self.data:  # O(n) 查找
            self.data.append(item)  # O(1) 插入
    
    def contains(self, item):
        return item in self.data  # O(n) 查找


class EfficientDataStructure:
    """
    优化版本：使用集合
    """
    def __init__(self):
        self.data = set()  # 使用集合存储
    
    def add(self, item):
        self.data.add(item)  # O(1) 平均时间复杂度
    
    def contains(self, item):
        return item in self.data  # O(1) 平均时间复杂度


def benchmark_function(func, *args, **kwargs):
    """
    性能测试辅助函数
    """
    start_time = time.time()
    result = func(*args, **kwargs)
    end_time = time.time()
    return result, end_time - start_time


# 性能测试代码
if __name__ == "__main__":
    print("性能问题示例测试:")
    print("=" * 50)
    
    # 测试1: 斐波那契数列
    print("1. 斐波那契数列性能对比 (n=30):")
    _, time1 = benchmark_function(inefficient_fibonacci, 30)
    _, time2 = benchmark_function(efficient_fibonacci, 30)
    print(f"   低效版本: {time1:.4f}秒")
    print(f"   高效版本: {time2:.6f}秒")
    print(f"   性能提升: {time1/time2:.1f}倍")
    
    # 测试2: 质数检查
    print("\n2. 质数检查性能对比 (n=100003):")
    large_num = 100003
    _, time1 = benchmark_function(inefficient_prime_check, large_num)
    _, time2 = benchmark_function(efficient_prime_check, large_num)
    print(f"   低效版本: {time1:.4f}秒")
    print(f"   高效版本: {time2:.6f}秒")
    print(f"   性能提升: {time1/time2:.1f}倍")
    
    # 测试3: 字符串拼接
    print("\n3. 字符串拼接性能对比 (1000个单词):")
    words = ["word"] * 1000
    _, time1 = benchmark_function(inefficient_string_concat, words)
    _, time2 = benchmark_function(efficient_string_concat, words)
    print(f"   低效版本: {time1:.4f}秒")
    print(f"   高效版本: {time2:.6f}秒")
    print(f"   性能提升: {time1/time2:.1f}倍")
    
    # 测试4: 内存使用对比
    print("\n4. 内存使用对比:")
    large_data = list(range(100000))
    
    # 测量内存使用（简化版本）
    import tracemalloc
    
    tracemalloc.start()
    result1 = memory_inefficient_processing(large_data)
    current, peak = tracemalloc.get_traced_memory()
    memory1 = peak
    tracemalloc.stop()
    
    tracemalloc.start()
    result2 = memory_efficient_processing(large_data)
    current, peak = tracemalloc.get_traced_memory()
    memory2 = peak
    tracemalloc.stop()
    
    print(f"   低效版本内存峰值: {memory1 / 1024 / 1024:.2f} MB")
    print(f"   高效版本内存峰值: {memory2 / 1024 / 1024:.2f} MB")
    print(f"   内存节省: {(memory1 - memory2) / 1024 / 1024:.2f} MB")
    
    # 测试5: 数据结构性能对比
    print("\n5. 数据结构性能对比 (10000次操作):")
    
    # 低效数据结构测试
    inefficient_ds = InefficientDataStructure()
    start_time = time.time()
    for i in range(1000):
        inefficient_ds.add(i)
    for i in range(500, 1500):
        inefficient_ds.contains(i)
    time1 = time.time() - start_time
    
    # 高效数据结构测试
    efficient_ds = EfficientDataStructure()
    start_time = time.time()
    for i in range(1000):
        efficient_ds.add(i)
    for i in range(500, 1500):
        efficient_ds.contains(i)
    time2 = time.time() - start_time
    
    print(f"   低效数据结构: {time1:.4f}秒")
    print(f"   高效数据结构: {time2:.6f}秒")
    print(f"   性能提升: {time1/time2:.1f}倍")
    
    print("\n" + "=" * 50)
    print("性能优化要点:")
    print("1. 选择合适的算法和数据结构")
    print("2. 避免不必要的重复计算")
    print("3. 使用内置函数和库")
    print("4. 注意内存使用效率")
    print("5. 使用缓存和记忆化")
