#include <iostream>
#include <vector>
#include <algorithm>

// 二分查找函数，在有序数组中查找目标值
// 参数:
//   arr: 已排序的整数数组
//   target: 要查找的目标值
// 返回值:
//   如果找到目标值，返回其索引；否则返回-1
int binarySearch(const std::vector<int>& arr, int target) {
    int left = 0;
    int right = arr.size() - 1;
    
    while (left <= right) {
        int mid = left + (right - left) / 2;
        
        if (arr[mid] == target) {
            return mid;
        } else if (arr[mid] < target) {
            left = mid + 1;
        } else {
            right = mid - 1;
        }
    }
    
    return -1; // 未找到目标值
}

// 主函数，演示二分查找的使用
int main() {
    std::vector<int> numbers = {1, 3, 5, 7, 9, 11, 13, 15, 17, 19};
    
    std::cout << "数组内容: ";
    // 遍历数组numbers，打印每个元素值
    for (const int& num : numbers) {
        std::cout << num << " ";
    }
    std::cout << std::endl;
    
    int target = 7;
    int result = binarySearch(numbers, target);
    
    if (result != -1) {
        std::cout << "找到了 " << target << "，位置在索引 " << result << std::endl;
    } else {
        std::cout << "未找到 " << target << std::endl;
    }
    
    target = 8;
    result = binarySearch(numbers, target);
    
    if (result != -1) {
        std::cout << "找到了 " << target << "，位置在索引 " << result << std::endl;
    } else {
        std::cout << "未找到 " << target << std::endl;
    }
    
    return 0;
}