"""
逻辑错误示例 - 条件判断和循环逻辑问题
这个文件包含了常见的逻辑错误，用于教学和调试练习
"""

def calculate_grade(score):
    """
    问题1: 边界条件判断错误
    计算学生成绩等级，但边界条件有问题
    """
    if score >= 90:
        return 'A'
    elif score >= 80:
        return 'B'
    elif score >= 70:
        return 'C'
    elif score >= 60:
        return 'D'
    else:
        return 'F'
    # 问题：没有处理负数或超过100的情况


def find_max_in_list(numbers):
    """
    问题2: 循环逻辑错误
    查找列表中的最大值，但逻辑有误
    """
    if not numbers:
        return None
    
    max_value = 0  # 问题：初始值设为0，如果所有数都是负数会出错
    for num in numbers:
        if num > max_value:
            max_value = num
    return max_value


def count_vowels(text):
    """
    问题3: 条件判断不完整
    统计文本中的元音字母数量
    """
    vowels = "aeiou"
    count = 0
    
    for char in text:
        if char in vowels:  # 问题：没有考虑大写字母
            count += 1
    
    return count


def is_prime(n):
    """
    问题4: 算法逻辑错误
    判断一个数是否为质数，但逻辑有问题
    """
    if n <= 1:
        return False
    
    for i in range(2, n):  # 问题：效率低，应该只检查到sqrt(n)
        if n % i == 0:
            return False
    return True


def fibonacci(n):
    """
    问题5: 递归边界条件错误
    计算斐波那契数列第n项
    """
    if n == 0:
        return 0
    elif n == 1:
        return 1
    # 问题：没有处理负数输入
    else:
        return fibonacci(n-1) + fibonacci(n-2)


def merge_sorted_lists(list1, list2):
    """
    问题6: 索引越界风险
    合并两个已排序的列表
    """
    result = []
    i, j = 0, 0
    
    while i < len(list1) and j < len(list2):
        if list1[i] <= list2[j]:
            result.append(list1[i])
            i += 1
        else:
            result.append(list2[j])
            j += 1
    
    # 问题：没有处理剩余元素
    return result


def calculate_average(numbers):
    """
    问题7: 除零错误风险
    计算数字列表的平均值
    """
    total = sum(numbers)
    return total / len(numbers)  # 问题：如果列表为空会除零错误


def remove_duplicates(lst):
    """
    问题8: 修改正在迭代的列表
    移除列表中的重复元素
    """
    for i in range(len(lst)):
        for j in range(i + 1, len(lst)):
            if lst[i] == lst[j]:
                lst.remove(lst[j])  # 问题：修改正在迭代的列表会导致索引错误
    return lst


def binary_search(arr, target):
    """
    问题9: 无限循环风险
    二分查找实现
    """
    left, right = 0, len(arr) - 1
    
    while left <= right:
        mid = (left + right) // 2
        if arr[mid] == target:
            return mid
        elif arr[mid] < target:
            left = mid  # 问题：应该是 mid + 1，否则可能无限循环
        else:
            right = mid - 1
    
    return -1


def password_strength(password):
    """
    问题10: 逻辑判断不严谨
    检查密码强度
    """
    if len(password) >= 8:
        if any(c.isupper() for c in password):
            if any(c.islower() for c in password):
                if any(c.isdigit() for c in password):
                    return "Strong"
    return "Weak"
    # 问题：嵌套太深，且没有检查特殊字符


# 测试代码
if __name__ == "__main__":
    # 这些测试会暴露上述函数中的问题
    print("测试逻辑错误示例:")
    
    # 测试1: 成绩计算
    print(f"成绩 105: {calculate_grade(105)}")  # 应该处理无效输入
    print(f"成绩 -10: {calculate_grade(-10)}")  # 应该处理负数
    
    # 测试2: 查找最大值
    print(f"负数列表最大值: {find_max_in_list([-5, -2, -10])}")  # 应该返回-2，但会返回0
    
    # 测试3: 元音计数
    print(f"'Hello World'中的元音: {count_vowels('Hello World')}")  # 应该包含大写字母
    
    # 测试4: 质数判断
    print(f"97是质数吗: {is_prime(97)}")  # 效率问题，大数会很慢
    
    # 测试5: 斐波那契
    try:
        print(f"fibonacci(-1): {fibonacci(-1)}")  # 会导致无限递归
    except RecursionError:
        print("递归错误：负数输入")
    
    # 测试6: 合并列表
    print(f"合并[1,3,5]和[2,4,6,8]: {merge_sorted_lists([1,3,5], [2,4,6,8])}")  # 丢失元素
    
    # 测试7: 平均值
    try:
        print(f"空列表平均值: {calculate_average([])}")
    except ZeroDivisionError:
        print("除零错误：空列表")
    
    # 测试8: 去重
    test_list = [1, 2, 2, 3, 3, 4]
    print(f"去重前: {test_list}")
    try:
        result = remove_duplicates(test_list.copy())
        print(f"去重后: {result}")
    except IndexError:
        print("索引错误：修改正在迭代的列表")
    
    # 测试9: 二分查找
    print(f"在[1,2,3,4,5]中查找3: {binary_search([1,2,3,4,5], 3)}")
    
    # 测试10: 密码强度
    print(f"密码'Abc123!@'强度: {password_strength('Abc123!@')}")
