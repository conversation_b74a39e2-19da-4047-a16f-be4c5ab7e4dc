/**
 * 主类，演示User和UserManager的使用
 */
public class Main {
    public static void main(String[] args) {
        // 创建用户管理器
        UserManager userManager = new UserManager();
        
        // 创建一些用户
        User user1 = new User(1, "张三", "<PERSON><PERSON><PERSON>@example.com");
        User user2 = new User(2, "李四", "<EMAIL>");
        User user3 = new User(3, "王五", "<EMAIL>");
        
        // 添加用户
        userManager.addUser(user1);
        userManager.addUser(user2);
        userManager.addUser(user3);
        
        // 显示所有用户
        System.out.println("所有用户:");
        userManager.getAllUsers().forEach(System.out::println);
        
        // 查找特定用户
        System.out.println("\n查找ID为2的用户:");
        userManager.findUserById(2).ifPresentOrElse(
            user -> System.out.println("找到用户: " + user),
            () -> System.out.println("未找到用户")
        );
        
        // 尝试查找不存在的用户
        System.out.println("\n查找ID为99的用户:");
        userManager.findUserById(99).ifPresentOrElse(
            user -> System.out.println("找到用户: " + user),
            () -> System.out.println("未找到用户")
        );
        
        // 删除用户
        System.out.println("\n删除ID为1的用户...");
        boolean deleted = userManager.deleteUser(1);
        System.out.println("删除成功: " + deleted);
        
        // 再次显示所有用户
        System.out.println("\n删除后的用户列表:");
        userManager.getAllUsers().forEach(System.out::println);
    }
}