import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 用户管理类，用于管理用户信息
 */
public class UserManager {
    private List<User> users;
    
    public UserManager() {
        this.users = new ArrayList<>();
    }
    
    /**
     * 添加用户
     * @param user 要添加的用户
     * @return 添加成功返回true，否则返回false
     */
    public boolean addUser(User user) {
        if (user == null || user.getName() == null || user.getEmail() == null) {
            return false;
        }
        return users.add(user);
    }
    
    /**
     * 根据ID查找用户
     * @param id 用户ID
     * @return 用户对象的Optional包装
     */
    public Optional<User> findUserById(int id) {
        return users.stream()
                   .filter(user -> user.getId() == id)
                   .findFirst();
    }
    
    /**
     * 获取所有用户
     * @return 用户列表
     */
    public List<User> getAllUsers() {
        return new ArrayList<>(users);
    }
    
    /**
     * 根据ID删除用户
     * @param id 要删除的用户ID
     * @return 删除成功返回true，否则返回false
     */
    public boolean deleteUser(int id) {
        return users.removeIf(user -> user.getId() == id);
    }
}