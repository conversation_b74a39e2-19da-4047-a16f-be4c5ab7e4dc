<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>五子棋游戏</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .game-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            text-align: center;
        }

        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .game-info {
            margin-bottom: 20px;
            font-size: 1.2em;
        }

        .current-player {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .game-status {
            font-size: 1.1em;
            margin-bottom: 15px;
            min-height: 25px;
        }

        .board-container {
            display: inline-block;
            background: #deb887;
            border: 3px solid #8b7355;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .board {
            display: grid;
            grid-template-columns: repeat(15, 30px);
            grid-template-rows: repeat(15, 30px);
            gap: 0;
            background: #deb887;
            position: relative;
        }

        .cell {
            width: 30px;
            height: 30px;
            border: 1px solid #8b7355;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            position: relative;
            background: #deb887;
        }

        .cell:hover {
            background: #d4af7a;
        }

        .cell.occupied {
            cursor: not-allowed;
        }

        .cell.occupied:hover {
            background: #deb887;
        }

        .piece {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: 2px solid #333;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .piece.black {
            background: #000;
            border-color: #333;
        }

        .piece.white {
            background: #fff;
            border-color: #666;
        }

        .controls {
            margin-top: 20px;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 0 10px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .winner-message {
            font-size: 1.5em;
            font-weight: bold;
            color: #e74c3c;
            margin: 15px 0;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .move-history {
            margin-top: 20px;
            text-align: left;
            max-height: 150px;
            overflow-y: auto;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }

        .move-history h3 {
            margin-top: 0;
            color: #333;
            text-align: center;
        }

        .move-item {
            padding: 2px 0;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>🔴⚫ 五子棋游戏 ⚫🔴</h1>
        
        <div class="game-info">
            <div class="current-player">
                当前玩家: <span id="currentPlayerDisplay">⚫ 黑棋</span>
            </div>
            <div class="game-status" id="gameStatus">点击棋盘开始游戏</div>
        </div>

        <div class="board-container">
            <div class="board" id="gameBoard"></div>
        </div>

        <div class="controls">
            <button class="btn" onclick="restartGame()">🔄 重新开始</button>
            <button class="btn" onclick="undoMove()" id="undoBtn">↩️ 悔棋</button>
        </div>

        <div class="move-history">
            <h3>📝 走棋记录</h3>
            <div id="moveList"></div>
        </div>
    </div>

    <script>
        // 游戏状态
        let currentPlayer = 'black'; // 'black' 或 'white'
        let gameBoard = Array(15).fill().map(() => Array(15).fill(null));
        let gameOver = false;
        let moveHistory = [];

        // 初始化游戏
        function initGame() {
            const board = document.getElementById('gameBoard');
            board.innerHTML = '';
            
            // 创建15x15的棋盘
            for (let row = 0; row < 15; row++) {
                for (let col = 0; col < 15; col++) {
                    const cell = document.createElement('div');
                    cell.className = 'cell';
                    cell.dataset.row = row;
                    cell.dataset.col = col;
                    cell.addEventListener('click', () => makeMove(row, col));
                    board.appendChild(cell);
                }
            }
            
            updateDisplay();
        }

        // 下棋
        function makeMove(row, col) {
            if (gameOver || gameBoard[row][col] !== null) {
                return;
            }

            // 在棋盘上放置棋子
            gameBoard[row][col] = currentPlayer;
            
            // 记录走棋
            moveHistory.push({ row, col, player: currentPlayer });
            
            // 在界面上显示棋子
            const cell = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
            const piece = document.createElement('div');
            piece.className = `piece ${currentPlayer}`;
            cell.appendChild(piece);
            cell.classList.add('occupied');

            // 检查胜利条件
            if (checkWin(row, col)) {
                gameOver = true;
                const winner = currentPlayer === 'black' ? '⚫ 黑棋' : '⚪ 白棋';
                document.getElementById('gameStatus').innerHTML = 
                    `<div class="winner-message">🎉 ${winner} 获胜！🎉</div>`;
                return;
            }

            // 检查平局
            if (moveHistory.length === 225) {
                gameOver = true;
                document.getElementById('gameStatus').innerHTML = 
                    '<div class="winner-message">🤝 平局！</div>';
                return;
            }

            // 切换玩家
            currentPlayer = currentPlayer === 'black' ? 'white' : 'black';
            updateDisplay();
        }

        // 检查胜利条件
        function checkWin(row, col) {
            const directions = [
                [0, 1],   // 水平
                [1, 0],   // 垂直
                [1, 1],   // 主对角线
                [1, -1]   // 副对角线
            ];

            for (const [dx, dy] of directions) {
                let count = 1; // 包含当前棋子

                // 向一个方向检查
                for (let i = 1; i < 5; i++) {
                    const newRow = row + dx * i;
                    const newCol = col + dy * i;
                    if (newRow >= 0 && newRow < 15 && newCol >= 0 && newCol < 15 &&
                        gameBoard[newRow][newCol] === currentPlayer) {
                        count++;
                    } else {
                        break;
                    }
                }

                // 向相反方向检查
                for (let i = 1; i < 5; i++) {
                    const newRow = row - dx * i;
                    const newCol = col - dy * i;
                    if (newRow >= 0 && newRow < 15 && newCol >= 0 && newCol < 15 &&
                        gameBoard[newRow][newCol] === currentPlayer) {
                        count++;
                    } else {
                        break;
                    }
                }

                if (count >= 5) {
                    return true;
                }
            }

            return false;
        }

        // 更新显示
        function updateDisplay() {
            const playerDisplay = document.getElementById('currentPlayerDisplay');
            const statusDisplay = document.getElementById('gameStatus');
            
            if (!gameOver) {
                playerDisplay.textContent = currentPlayer === 'black' ? '⚫ 黑棋' : '⚪ 白棋';
                statusDisplay.textContent = '请下棋';
            }
            
            updateMoveHistory();
            updateUndoButton();
        }

        // 更新走棋记录
        function updateMoveHistory() {
            const moveList = document.getElementById('moveList');
            moveList.innerHTML = '';
            
            moveHistory.forEach((move, index) => {
                const moveItem = document.createElement('div');
                moveItem.className = 'move-item';
                const player = move.player === 'black' ? '⚫' : '⚪';
                const position = String.fromCharCode(65 + move.col) + (move.row + 1);
                moveItem.textContent = `${index + 1}. ${player} ${position}`;
                moveList.appendChild(moveItem);
            });
            
            // 滚动到最新记录
            moveList.scrollTop = moveList.scrollHeight;
        }

        // 更新悔棋按钮状态
        function updateUndoButton() {
            const undoBtn = document.getElementById('undoBtn');
            undoBtn.disabled = moveHistory.length === 0 || gameOver;
            undoBtn.style.opacity = undoBtn.disabled ? '0.5' : '1';
        }

        // 悔棋
        function undoMove() {
            if (moveHistory.length === 0 || gameOver) {
                return;
            }

            const lastMove = moveHistory.pop();
            const { row, col } = lastMove;
            
            // 清除棋盘上的棋子
            gameBoard[row][col] = null;
            
            // 清除界面上的棋子
            const cell = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
            cell.innerHTML = '';
            cell.classList.remove('occupied');
            
            // 切换回上一个玩家
            currentPlayer = currentPlayer === 'black' ? 'white' : 'black';
            
            updateDisplay();
        }

        // 重新开始游戏
        function restartGame() {
            currentPlayer = 'black';
            gameBoard = Array(15).fill().map(() => Array(15).fill(null));
            gameOver = false;
            moveHistory = [];
            
            // 清除所有棋子
            const cells = document.querySelectorAll('.cell');
            cells.forEach(cell => {
                cell.innerHTML = '';
                cell.classList.remove('occupied');
            });
            
            updateDisplay();
            document.getElementById('gameStatus').textContent = '点击棋盘开始游戏';
        }

        // 页面加载完成后初始化游戏
        document.addEventListener('DOMContentLoaded', initGame);
    </script>
</body>
</html>
