<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>五子棋游戏</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .game-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            text-align: center;
        }

        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .game-info {
            margin-bottom: 20px;
            font-size: 1.2em;
        }

        .current-player {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .game-status {
            font-size: 1.1em;
            margin-bottom: 15px;
            min-height: 25px;
        }

        .board-container {
            display: inline-block;
            background: #deb887;
            border: 3px solid #8b7355;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .board {
            display: grid;
            grid-template-columns: repeat(15, 30px);
            grid-template-rows: repeat(15, 30px);
            gap: 0;
            background: #deb887;
            position: relative;
        }

        .cell {
            width: 30px;
            height: 30px;
            border: 1px solid #8b7355;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            position: relative;
            background: #deb887;
        }

        .cell:hover {
            background: #d4af7a;
        }

        .cell.occupied {
            cursor: not-allowed;
        }

        .cell.occupied:hover {
            background: #deb887;
        }

        .piece {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: 2px solid #333;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .piece.black {
            background: #000;
            border-color: #333;
        }

        .piece.white {
            background: #fff;
            border-color: #666;
        }

        .controls {
            margin-top: 20px;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 0 10px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .winner-message {
            font-size: 1.5em;
            font-weight: bold;
            color: #e74c3c;
            margin: 15px 0;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .move-history {
            margin-top: 20px;
            text-align: left;
            max-height: 150px;
            overflow-y: auto;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }

        .move-history h3 {
            margin-top: 0;
            color: #333;
            text-align: center;
        }

        .move-item {
            padding: 2px 0;
            font-size: 0.9em;
        }

        .shortcuts-info {
            margin-top: 15px;
            font-size: 0.85em;
            color: #666;
            text-align: center;
            padding: 8px;
            background: #f0f0f0;
            border-radius: 5px;
        }

        .shortcuts-info kbd {
            background: #fff;
            border: 1px solid #ccc;
            border-radius: 3px;
            padding: 2px 6px;
            font-family: monospace;
            font-size: 0.9em;
            margin: 0 2px;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>🔴⚫ 五子棋游戏 ⚫🔴</h1>
        
        <div class="game-info">
            <div class="current-player">
                当前玩家: <span id="currentPlayerDisplay">⚫ 黑棋</span>
            </div>
            <div class="game-status" id="gameStatus">点击棋盘开始游戏</div>
        </div>

        <div class="board-container">
            <div class="board" id="gameBoard"></div>
        </div>

        <div class="controls">
            <button class="btn" onclick="restartGame()">🔄 重新开始</button>
            <button class="btn" onclick="undoMove()" id="undoBtn">↩️ 悔棋</button>
        </div>

        <div class="move-history">
            <h3>📝 走棋记录</h3>
            <div id="moveList"></div>
        </div>

        <div class="shortcuts-info">
            <strong>⌨️ 快捷键:</strong>
            <kbd>Ctrl+Z</kbd> 悔棋 |
            <kbd>Ctrl+R</kbd> 重新开始 |
            <kbd>ESC</kbd> 重新开始
        </div>
    </div>

    <script>
        // 游戏常量
        const BOARD_SIZE = 15;
        const WIN_COUNT = 5;
        const TOTAL_CELLS = BOARD_SIZE * BOARD_SIZE;

        // 游戏状态
        let currentPlayer = 'black'; // 'black' 或 'white'
        let gameBoard = Array(BOARD_SIZE).fill().map(() => Array(BOARD_SIZE).fill(null));
        let gameOver = false;
        let moveHistory = [];
        let audioContext = null;

        // 初始化音频上下文
        function initAudio() {
            try {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
            } catch (e) {
                console.log('Web Audio API not supported');
            }
        }

        // 播放音效
        function playSound(frequency, duration = 100, type = 'sine') {
            if (!audioContext) return;

            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
            oscillator.type = type;

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration / 1000);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + duration / 1000);
        }

        // 初始化游戏
        function initGame() {
            const board = document.getElementById('gameBoard');
            board.innerHTML = '';

            // 创建棋盘
            for (let row = 0; row < BOARD_SIZE; row++) {
                for (let col = 0; col < BOARD_SIZE; col++) {
                    const cell = document.createElement('div');
                    cell.className = 'cell';
                    cell.dataset.row = row;
                    cell.dataset.col = col;
                    cell.addEventListener('click', () => makeMove(row, col));
                    board.appendChild(cell);
                }
            }

            updateDisplay();
        }

        // 下棋
        function makeMove(row, col) {
            if (gameOver || gameBoard[row][col] !== null) {
                return;
            }

            // 在棋盘上放置棋子
            gameBoard[row][col] = currentPlayer;
            
            // 记录走棋
            const moveIndex = moveHistory.length;
            const move = { row, col, player: currentPlayer };
            moveHistory.push(move);

            // 添加到历史记录显示
            addMoveToHistory(move, moveIndex);
            
            // 在界面上显示棋子
            const cell = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
            const piece = document.createElement('div');
            piece.className = `piece ${currentPlayer}`;
            cell.appendChild(piece);
            cell.classList.add('occupied');

            // 播放下棋音效
            playSound(currentPlayer === 'black' ? 440 : 523, 150);

            // 检查胜利条件
            if (checkWin(row, col)) {
                gameOver = true;
                const winner = currentPlayer === 'black' ? '⚫ 黑棋' : '⚪ 白棋';
                const statusElement = document.getElementById('gameStatus');
                statusElement.innerHTML = '';
                const winnerDiv = document.createElement('div');
                winnerDiv.className = 'winner-message';
                winnerDiv.textContent = `🎉 ${winner} 获胜！🎉`;
                statusElement.appendChild(winnerDiv);
                return;
            }

            // 检查平局
            if (moveHistory.length === TOTAL_CELLS) {
                gameOver = true;
                const statusElement = document.getElementById('gameStatus');
                statusElement.innerHTML = '';
                const drawDiv = document.createElement('div');
                drawDiv.className = 'winner-message';
                drawDiv.textContent = '🤝 平局！';
                statusElement.appendChild(drawDiv);
                return;
            }

            // 切换玩家
            currentPlayer = currentPlayer === 'black' ? 'white' : 'black';
            updateDisplay();
        }

        // 辅助函数：检查指定方向的连续棋子数量
        function countInDirection(row, col, dx, dy, player) {
            let count = 0;
            let currentRow = row;
            let currentCol = col;

            while (currentRow >= 0 && currentRow < BOARD_SIZE &&
                   currentCol >= 0 && currentCol < BOARD_SIZE &&
                   gameBoard[currentRow][currentCol] === player) {
                count++;
                currentRow += dx;
                currentCol += dy;
            }

            return count;
        }

        // 检查胜利条件
        function checkWin(row, col) {
            const directions = [
                [0, 1],   // 水平
                [1, 0],   // 垂直
                [1, 1],   // 主对角线
                [1, -1]   // 副对角线
            ];

            for (const [dx, dy] of directions) {
                // 向正方向计数（不包括当前位置）
                const positiveCount = countInDirection(row + dx, col + dy, dx, dy, currentPlayer);
                // 向负方向计数（不包括当前位置）
                const negativeCount = countInDirection(row - dx, col - dy, -dx, -dy, currentPlayer);
                // 总数 = 正方向 + 负方向 + 当前位置(1)
                const totalCount = positiveCount + negativeCount + 1;

                if (totalCount >= WIN_COUNT) {
                    return true;
                }
            }

            return false;
        }

        // 更新显示
        function updateDisplay() {
            const playerDisplay = document.getElementById('currentPlayerDisplay');
            const statusDisplay = document.getElementById('gameStatus');
            
            if (!gameOver) {
                playerDisplay.textContent = currentPlayer === 'black' ? '⚫ 黑棋' : '⚪ 白棋';
                statusDisplay.textContent = '请下棋';
            }
            
            updateUndoButton();
        }

        // 添加单个走棋记录
        function addMoveToHistory(move, index) {
            const moveList = document.getElementById('moveList');
            const moveItem = document.createElement('div');
            moveItem.className = 'move-item';
            const player = move.player === 'black' ? '⚫' : '⚪';
            const position = String.fromCharCode(65 + move.col) + (move.row + 1);
            moveItem.textContent = `${index + 1}. ${player} ${position}`;
            moveList.appendChild(moveItem);

            // 滚动到最新记录
            moveList.scrollTop = moveList.scrollHeight;
        }

        // 更新走棋记录（完整重建，用于重置游戏）
        function updateMoveHistory() {
            const moveList = document.getElementById('moveList');
            moveList.innerHTML = '';

            moveHistory.forEach((move, index) => {
                addMoveToHistory(move, index);
            });
        }

        // 移除最后一个走棋记录
        function removeLastMoveFromHistory() {
            const moveList = document.getElementById('moveList');
            const lastItem = moveList.lastElementChild;
            if (lastItem) {
                lastItem.remove();
            }
        }

        // 更新悔棋按钮状态
        function updateUndoButton() {
            const undoBtn = document.getElementById('undoBtn');
            undoBtn.disabled = moveHistory.length === 0 || gameOver;
            undoBtn.style.opacity = undoBtn.disabled ? '0.5' : '1';
        }

        // 悔棋
        function undoMove() {
            if (moveHistory.length === 0 || gameOver) {
                return;
            }

            const lastMove = moveHistory.pop();
            const { row, col } = lastMove;

            // 清除棋盘上的棋子
            gameBoard[row][col] = null;

            // 清除界面上的棋子
            const cell = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
            cell.innerHTML = '';
            cell.classList.remove('occupied');

            // 移除历史记录显示
            removeLastMoveFromHistory();

            // 切换回上一个玩家
            currentPlayer = currentPlayer === 'black' ? 'white' : 'black';

            updateDisplay();
        }

        // 重新开始游戏
        function restartGame() {
            currentPlayer = 'black';
            gameBoard = Array(BOARD_SIZE).fill().map(() => Array(BOARD_SIZE).fill(null));
            gameOver = false;
            moveHistory = [];

            // 清除所有棋子
            const cells = document.querySelectorAll('.cell');
            cells.forEach(cell => {
                cell.innerHTML = '';
                cell.classList.remove('occupied');
            });

            updateDisplay();
            document.getElementById('gameStatus').textContent = '点击棋盘开始游戏';
        }

        // 键盘快捷键支持
        function handleKeyboardShortcuts(event) {
            // Ctrl+Z: 悔棋
            if (event.ctrlKey && event.key.toLowerCase() === 'z') {
                event.preventDefault();
                undoMove();
            }
            // Ctrl+R: 重新开始（需要确认）
            else if (event.ctrlKey && event.key.toLowerCase() === 'r') {
                event.preventDefault();
                if (confirm('确定要重新开始游戏吗？')) {
                    restartGame();
                }
            }
            // ESC: 重新开始（需要确认）
            else if (event.key === 'Escape') {
                if (confirm('确定要重新开始游戏吗？')) {
                    restartGame();
                }
            }
        }

        // 页面加载完成后初始化游戏
        document.addEventListener('DOMContentLoaded', function() {
            initGame();
            // 添加键盘事件监听
            document.addEventListener('keydown', handleKeyboardShortcuts);
        });
    </script>
</body>
</html>
