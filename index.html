<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的网页</title>
    <meta name="description" content="一个基本的HTML页面示例">
    <title>我的网页 - 欢迎页面</title>
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2980b9;
            --text-color: #333;
            --light-text: #666;
            --bg-color: #f5f5f5;
            --card-bg: #ffffff;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        body {
            font-family: Arial, sans-serif;
            font-family: 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
            margin: 40px;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            background-color: var(--bg-color);
            color: var(--text-color);
            line-height: 1.6;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            margin: 40px auto;
            padding: 30px;
            background-color: var(--card-bg);
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            box-shadow: var(--shadow);
        }
        
        h1 {
            color: #333;
            color: var(--primary-color);
            margin-top: 0;
            font-size: 2.2rem;
            font-weight: 600;
        }
        
        p {
            color: #666;
            line-height: 1.6;
            color: var(--light-text);
            margin-bottom: 1.2em;
        }
        
        .highlight {
            color: var(--secondary-color);
            font-weight: 500;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 20px;
                padding: 20px;
            }
            
            h1 {
                font-size: 1.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>欢迎来到我的网页</h1>
        <p>这是一个基本的HTML页面示例。你可以根据需要修改内容和样式。</p>
        <p>如果你有特定的需求，请告诉我，我可以帮你创建更符合要求的页面。</p>
        <p>这是一个基本的HTML页面示例。您可以根据需要修改内容和样式。</p>
        <p class="highlight">如果您有特定的需求，请告诉我，我可以帮您创建更符合要求的页面。</p>
    </div>
</body>
</html>